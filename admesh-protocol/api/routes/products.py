from fastapi import APIRouter, HTTPException, Depends, Query
from firebase.config import get_db
from auth.deps import require_role, verify_firebase_token
from google.cloud import firestore
from pydantic import BaseModel, HttpUrl
from typing import Optional, List
import logging
import uuid

router = APIRouter()
db = get_db()
logger = logging.getLogger(__name__)

# --------------------------------
# Pydantic Models
# --------------------------------

class ProductCreate(BaseModel):
    title: str
    url: HttpUrl
    admesh_link: Optional[HttpUrl] = None
    categories: Optional[list[str]] = []  # Changed from single category to multiple categories
    keywords: Optional[list[str]] = []
    audience: Optional[str] = None
    confidence_score: float = 50.0
    description: Optional[str] = None
    pricing_url: Optional[HttpUrl] = None
    audience_segment: Optional[str] = None
    integration_list: Optional[list[str]] = []

class ProductUpdate(BaseModel):
    title: Optional[str] = None
    url: Optional[HttpUrl] = None
    admesh_link: Optional[HttpUrl] = None
    categories: Optional[list[str]] = None  # Changed from category to categories
    keywords: Optional[list[str]] = None
    audience: Optional[str] = None
    confidence_score: Optional[float] = None
    description: Optional[str] = None
    status: Optional[str] = None
    pricing_url: Optional[HttpUrl] = None
    audience_segment: Optional[str] = None
    integration_list: Optional[list[str]] = None

# --------------------------------
# Create Product
# --------------------------------
@router.post("/create")
async def create_product(data: ProductCreate, user=Depends(require_role("brand"))):
    # Import URL normalization function and slug generation from openrouter
    from api.routes.openrouter import normalize_url, generate_slug
    from urllib.parse import urlparse
    # Removed subscription limits functionality

    # Normalize the URL for comparison
    normalized_url = normalize_url(str(data.url))

    # Extract domain from URL for logging
    parsed_url = urlparse(str(data.url))
    domain = parsed_url.netloc.lower().replace("www.", "")

    # Generate slug from title
    from api.routes.openrouter import generate_slug
    title_slug = generate_slug(data.title)

    # First check if a product with the same slug exists
    slug_query = db.collection("products").where("slug", "==", title_slug).limit(1).stream()
    existing_products_by_slug = list(slug_query)

    # If no match by slug, check by normalized URL
    if not existing_products_by_slug:
        url_query = db.collection("products").where("normalized_url", "==", normalized_url).limit(1).stream()
        existing_products_by_slug = list(url_query)

    # Fallback: check by regular URL if no normalized URL matches found
    # This handles older products that might not have normalized_url field
    if not existing_products_by_slug:
        fallback_url_query = db.collection("products").where("url", "==", str(data.url)).limit(1).stream()
        existing_products_by_slug = list(fallback_url_query)

    if existing_products_by_slug:
        # Product with same URL/domain exists, update the brand_id
        existing_product = existing_products_by_slug[0]
        existing_product_id = existing_product.id

        # Update ONLY the brand_id as requested
        product_ref = db.collection("products").document(existing_product_id)
        product_ref.update({"brand_id": user["uid"]})

        # Add to brand's active_products
        brand_ref = db.collection("brands").document(user["uid"])
        brand_ref.update({
            "active_products": firestore.ArrayUnion([existing_product_id]),
            "inactive_products": firestore.ArrayRemove([existing_product_id])
        })

        logger.info(f"Product with domain {domain} already exists. Updated brand_id for product: {existing_product_id} to brand {user['uid']}")
        return {"status": "success", "product_id": existing_product_id, "message": "Existing product updated with your brand ID"}

    # Check if a product with the same title already exists for this brand
    title_query = db.collection("products").where("title", "==", data.title).where("brand_id", "==", user["uid"]).limit(1).stream()
    title_exists = any(title_query)

    if title_exists:
        raise HTTPException(status_code=400, detail="Product with this title already exists for your brand")

    # Generate a UUID for the product ID
    product_id = str(uuid.uuid4())
    product_ref = db.collection("products").document(product_id)

    # ✅ Serialize all fields properly
    product_data = {
        **data.model_dump(mode="json"),
        "id": product_id,
        "brand_id": user["uid"],
        "active_offers": [],
        "inactive_offers": [],
        "created_at": firestore.SERVER_TIMESTAMP,
        "view_count": 0,
        "clicks": 0,
        "conversions": 0,
        "status": "active",
        "normalized_url": normalized_url,
        "slug": title_slug
    }

    product_ref.set(product_data)

    # Add to brand's active_products
    brand_ref = db.collection("brands").document(user["uid"])
    brand_ref.update({
        "active_products": firestore.ArrayUnion([product_id]),
        "inactive_products": firestore.ArrayRemove([product_id])
    })

    # Removed subscription product count tracking

    logger.info(f"Product created: {product_id} by brand {user['uid']}")
    return {"status": "success", "product_id": product_id}

# --------------------------------
# Update Product
# --------------------------------
@router.patch("/{product_id}")
async def update_product(product_id: str, update: ProductUpdate, user=Depends(require_role("brand"))):
    product_ref = db.collection("products").document(product_id)
    if not product_ref.get().exists:
        raise HTTPException(status_code=404, detail="Product not found")

    # Get the product to check ownership
    product = product_ref.get().to_dict()
    brand_id = product["brand_id"]

    # Security check: ensure the user can only update their own products
    if brand_id != user["uid"]:
        logger.warning(f"Unauthorized update attempt: User {user['uid']} tried to update product {product_id} owned by {brand_id}")
        raise HTTPException(status_code=403, detail="You can only update your own products")

    # Update the product
    update_data = update.model_dump(exclude_unset=True)
    update_data["updated_at"] = firestore.SERVER_TIMESTAMP

    # If title is being updated, also update slug
    if "title" in update_data:
        from api.routes.openrouter import generate_slug
        update_data["slug"] = generate_slug(update_data["title"])

    # If URL is being updated, also update normalized_url
    if "url" in update_data:
        from api.routes.openrouter import normalize_url
        update_data["normalized_url"] = normalize_url(str(update_data["url"]))

    product_ref.update(update_data)

    logger.info(f"Product updated: {product_id} by brand {brand_id}")
    return {"status": "success", "message": "Product updated successfully"}

# --------------------------------
# Get Single Product
# --------------------------------
@router.get("/{product_id}")
async def get_product(product_id: str, user=Depends(require_role("brand"))):
    product_ref = db.collection("products").document(product_id)
    product_doc = product_ref.get()

    if not product_doc.exists:
        raise HTTPException(status_code=404, detail="Product not found")

    product = product_doc.to_dict()
    brand_id = product["brand_id"]

    # Security check: ensure the user can only access their own products
    if brand_id != user["uid"]:
        logger.warning(f"Unauthorized access attempt: User {user['uid']} tried to access product {product_id} owned by {brand_id}")
        raise HTTPException(status_code=403, detail="You can only access your own products")

    # Add the document ID to the product data
    product["id"] = product_id

    logger.info(f"Product fetched: {product_id} by brand {brand_id}")
    return product

# --------------------------------
# Delete Product
# --------------------------------
@router.delete("/{product_id}")
async def delete_product(product_id: str, user=Depends(require_role("brand"))):
    product_ref = db.collection("products").document(product_id)
    if not product_ref.get().exists:
        raise HTTPException(status_code=404, detail="Product not found")

    product = product_ref.get().to_dict()
    brand_id = product["brand_id"]

    # Security check: ensure the user can only delete their own products
    if brand_id != user["uid"]:
        logger.warning(f"Unauthorized delete attempt: User {user['uid']} tried to delete product {product_id} owned by {brand_id}")
        raise HTTPException(status_code=403, detail="You can only delete your own products")

    # Check if product has active offers
    active_offers = product.get("active_offers", [])
    if active_offers:
        logger.warning(f"Cannot delete product with active offers: {product_id}")
        raise HTTPException(status_code=400, detail="Cannot delete product with active offers. Please deactivate all offers first.")

    # Delete the product
    product_ref.delete()
    logger.info(f"Product deleted: {product_id} by brand {brand_id}")

    # Remove from brand lists
    brand_ref = db.collection("brands").document(brand_id)
    brand_ref.update({
        "active_products": firestore.ArrayRemove([product_id]),
        "inactive_products": firestore.ArrayRemove([product_id])
    })

    return {"status": "success", "message": "Product deleted successfully"}

@router.get("/brand/all")
async def get_my_products(user=Depends(require_role("brand"))):
    logger.info("Fetching all products for brand")
    # Use the user object from the dependency directly
    brand_id = user["uid"]

    # Query Firestore for products with this brand_id
    query = db.collection("products").where("brand_id", "==", brand_id)
    snapshot = query.stream()
    products = [doc.to_dict() | {"id": doc.id} for doc in snapshot]

    return {
        "status": "success",
        "products": products
    }

@router.get("/brand/names")
async def get_product_names(user=Depends(require_role("brand"))):
    logger.info("Fetching product names for brand")
    # Use the user object from the dependency directly
    brand_id = user["uid"]

    # Fetch only the "title" field for products that belong to this brand
    query = db.collection("products") \
              .where("brand_id", "==", brand_id) \
              .select(["title"])  # Only select the 'title' field

    snapshot = query.stream()
    product_titles = [{"id": doc.id, "title": doc.to_dict().get("title")} for doc in snapshot]

    return {
        "status": "success",
        "products": product_titles
    }

# --------------------------------
# Admin Endpoints
# --------------------------------

@router.get("/admin/all")
async def admin_get_all_products(
    limit: int = Query(50, description="Maximum number of products to return"),
    offset: int = Query(0, description="Number of products to skip"),
    sort_by: str = Query(None, description="Field to sort by"),
    sort_direction: str = Query("desc", description="Sort direction (asc or desc)"),
    min_views: int = Query(None, description="Minimum view count"),
    min_clicks: int = Query(None, description="Minimum clicks"),
    min_conversions: int = Query(None, description="Minimum conversions"),
    decoded_token = Depends(verify_firebase_token)
):
    """Admin endpoint to get all products regardless of brand"""
    # Check if the user is an admin
    is_admin = decoded_token.get("admin", False)
    if not is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")

    logger.info(f"Admin fetching all products (limit: {limit}, offset: {offset}, sort_by: {sort_by}, sort_direction: {sort_direction})")

    try:
        # Start with base query
        query = db.collection("products")

        # Apply filters if provided
        if min_views is not None:
            query = query.where("view_count", ">=", min_views)
        if min_clicks is not None:
            query = query.where("clicks", ">=", min_clicks)
        if min_conversions is not None:
            query = query.where("conversions", ">=", min_conversions)

        # Apply sorting if provided
        if sort_by and sort_by in ["title", "view_count", "clicks", "conversions", "created_at"]:
            direction = firestore.Query.DESCENDING if sort_direction == "desc" else firestore.Query.ASCENDING
            query = query.order_by(sort_by, direction=direction)

        # Apply pagination
        query = query.limit(limit).offset(offset)

        # Execute query
        snapshot = query.stream()

        products = []
        for doc in snapshot:
            product_data = doc.to_dict()
            # Ensure id is included
            if "id" not in product_data:
                product_data["id"] = doc.id
            products.append(product_data)

        # Get total count for pagination
        # Note: This is inefficient for large collections, but works for now
        # In a production environment, you'd want to use a counter or estimate
        total_count = len(list(db.collection("products").select([]).stream()))

        return {
            "status": "success",
            "products": products,
            "pagination": {
                "total": total_count,
                "limit": limit,
                "offset": offset
            }
        }
    except Exception as e:
        logger.error(f"Error fetching products for admin: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch products: {str(e)}")

@router.delete("/admin/{product_id}")
async def admin_delete_product(
    product_id: str,
    decoded_token = Depends(verify_firebase_token)
):
    """Admin endpoint to delete any product regardless of brand"""
    # Check if the user is an admin
    is_admin = decoded_token.get("admin", False)
    if not is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")

    logger.info(f"Admin attempting to delete product: {product_id}")

    try:
        product_ref = db.collection("products").document(product_id)
        if not product_ref.get().exists:
            raise HTTPException(status_code=404, detail="Product not found")

        product = product_ref.get().to_dict()
        brand_id = product.get("brand_id")

        # Check if product has active offers
        active_offers = product.get("active_offers", [])
        if active_offers:
            logger.warning(f"Admin attempted to delete product with active offers: {product_id}")
            raise HTTPException(
                status_code=400,
                detail="Cannot delete product with active offers. Please deactivate all offers first."
            )

        # Delete the product
        product_ref.delete()
        logger.info(f"Product deleted by admin: {product_id}")

        # If we have a brand_id, update the brand's product lists
        if brand_id:
            brand_ref = db.collection("brands").document(brand_id)
            if brand_ref.get().exists:
                brand_ref.update({
                    "active_products": firestore.ArrayRemove([product_id]),
                    "inactive_products": firestore.ArrayRemove([product_id])
                })

        return {
            "status": "success",
            "message": "Product deleted successfully by admin"
        }
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error deleting product by admin: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete product: {str(e)}")
